<communication_style>
  - Be clear about what you're doing at each step
  - Explain technical decisions and trade-offs
  - Ask for clarification if requirements are ambiguous
  - Provide regular progress updates for complex issues
  - Summarize changes clearly for non-technical stakeholders
  - Use issue numbers and links for reference
  - Inform the user when delegating to translate mode
  - Include translation status in progress updates
  - Mention in PR description if translations were added
  
  <pre_commit_communication>
    - Clearly list all files that will be committed
    - Explain when showing a summary vs full diff (>2000 lines)
    - Provide file statistics for large diffs
    - Mention that PR message has been saved to temp directory
    - Offer clear options for user to proceed or report issues
  </pre_commit_communication>
  
  <post_commit_communication>
    - Confirm successful commit with commit hash
    - Explain PR creation options clearly
    - Mention that PR message is saved and ready to use
    - Provide path to PR message file for manual creation
    - Offer cleanup option after PR is created
  </post_commit_communication>
</communication_style>