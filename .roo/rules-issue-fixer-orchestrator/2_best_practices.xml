<best_practices>
  - Always read the entire issue and all comments before starting
  - Follow the project's coding standards and patterns
  - Focus exclusively on addressing the issue's requirements.
  - Make minimal, high-quality changes for bug fixes. The goal is a narrow, targeted fix, not a one-line hack.
  - Test thoroughly - both automated and manual testing
  - Document complex logic with comments
  - Keep commits focused and well-described
  - Reference the issue number in commits
  - Verify all acceptance criteria are met
  - Consider performance and security implications
  - Update documentation when needed
  - Add tests for any new functionality
  - Check for accessibility issues (for UI changes)
  - Always delegate translation tasks to translate mode when implementing user-facing changes
  - Check all modified files for hard-coded strings and internationalization needs
  - Wait for translation completion before proceeding to PR creation
  - Translation is required for:
    - Any new or modified UI components (.tsx, .jsx files)
    - User-facing documentation changes (.md files)
    - Error messages and notifications
    - Any strings visible to end users
  - The translate mode will handle:
    - Adding translations to all supported language files
    - Ensuring consistency with existing terminology
    - Maintaining sync across all language resources
  
  <commit_and_pr_best_practices>
    <principle>Always verify files before committing</principle>
    <guidelines>
      - Review git status to ensure only intended files are staged
      - Stage only files listed in modified_files.json
      - Never commit unrelated changes or temporary files
      - Always get user confirmation before committing
    </guidelines>
    
    <diff_management>
      - Save full diff to staged_changes.diff for review
      - If diff exceeds 2000 lines, create a summary instead
      - Summary should include file stats and change types
      - Always inform user when showing summary vs full diff
    </diff_management>
    
    <pr_message_handling>
      - Save PR message in both JSON and Markdown formats
      - pr_summary.json for programmatic use
      - pr_message.md for manual PR creation
      - Include all standard template sections
      - Make PR message self-contained and ready to use
    </pr_message_handling>
    
    <user_confirmation_flow>
      - Always ask for confirmation with clear options
      - First option should be "Looks good, go ahead"
      - Provide options for testing and issue reporting
      - Allow PR message modification before proceeding
      - Handle each user response appropriately
    </user_confirmation_flow>
    
    <temp_file_management>
      - All delegated tasks must save outputs to .roo/temp/issue-fixer-orchestrator/[TASK_ID]/
      - Keep all context files until user confirms cleanup
      - Offer cleanup option after PR creation
      - Never delete files without user permission
    </temp_file_management>
  </commit_and_pr_best_practices>
  <codebase_exploration_guidelines>
    <principle>Always use `codebase_search` FIRST to understand the codebase structure and find all related files before using other tools like `read_file`.</principle>
    
    <architectural_understanding>
      <title>Critical: Understand Component Interactions</title>
      <mandatory_steps>
        <step>Map the complete data flow from input to output</step>
        <step>Identify ALL paired operations (import/export, save/load, encode/decode)</step>
        <step>Find all consumers and dependencies of the affected code</step>
        <step>Trace how data transformations occur throughout the system</step>
        <step>Understand error propagation and handling patterns</step>
      </mandatory_steps>
    </architectural_understanding>
    
    <for_bug_fixes>
      <title>Investigation Checklist for Bug Fixes</title>
      <item>Search for the specific error message or broken functionality.</item>
      <item>Find all relevant error handling and logging statements.</item>
      <item>Locate related test files to understand expected behavior.</item>
      <item>Identify all dependencies and import/export patterns for the affected code.</item>
      <item>Find similar, working patterns in the codebase to use as a reference.</item>
      <item>**CRITICAL**: For any operation being fixed, find and analyze its paired operations</item>
      <item>Trace the complete data flow to understand all affected components</item>
    </for_bug_fixes>
    
    <for_features>
      <title>Investigation Checklist for New Features</title>
      <item>Search for any similar existing features to use as a blueprint.</item>
      <item>Find potential integration points (e.g., API routes, UI component registries).</item>
      <item>Locate relevant configuration files that may need to be updated.</item>
      <item>Identify common patterns, components, and utilities that should be reused.</item>
      <item>**CRITICAL**: Design paired operations together (e.g., both import AND export)</item>
      <item>Map all data transformations and state changes</item>
      <item>Identify all downstream consumers of the new functionality</item>
    </for_features>

    <paired_operations_principle>
      <title>Always Implement Paired Operations Together</title>
      <examples>
        <example>When fixing export, ALWAYS check and update import</example>
        <example>When modifying save, ALWAYS verify load handles the changes</example>
        <example>When changing serialization, ALWAYS update deserialization</example>
        <example>When updating create, consider read/update/delete operations</example>
      </examples>
      <rationale>
        Paired operations must maintain consistency. Changes to one without the other leads to data corruption, import failures, or broken functionality.
      </rationale>
    </paired_operations_principle>

    <critical_note>
      Always read multiple related files together to understand the full context. Never assume a change is isolated - trace its impact through the entire system.
    </critical_note>
  </codebase_exploration_guidelines>
</best_practices>