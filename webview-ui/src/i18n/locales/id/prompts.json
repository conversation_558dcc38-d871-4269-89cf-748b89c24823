{"title": "Mode", "done": "Se<PERSON><PERSON>", "modes": {"title": "Mode", "createNewMode": "Buat mode baru", "importMode": "Impor mode", "noMatchFound": "Tidak ada mode yang di<PERSON>ukan", "editModesConfig": "Edit konfigurasi mode", "editGlobalModes": "Edit Mode Global", "editProjectModes": "Edit Mode Proyek (.room<PERSON>)", "createModeHelpText": "Mode adalah persona khusus yang menyesuaikan perilaku Roo Code. <0>Pelajari tentang Menggunakan Mode</0> atau <1>Menyesuaikan Mode.</1>", "selectMode": "Cari mode"}, "apiConfiguration": {"title": "Konfigurasi API", "select": "Pilih konfigurasi API mana yang akan digunakan untuk mode ini"}, "tools": {"title": "Tools yang Tersedia", "builtInModesText": "Tools untuk mode bawaan tidak dapat dimodifikasi", "editTools": "Edit tools", "doneEditing": "<PERSON><PERSON><PERSON> mengedit", "allowedFiles": "File yang diizinkan:", "toolNames": {"read": "Baca File", "edit": "Edit File", "browser": "<PERSON><PERSON><PERSON>", "command": "Jalankan <PERSON>ah", "mcp": "Gunakan MCP"}, "noTools": "Tidak Ada"}, "roleDefinition": {"title": "<PERSON><PERSON><PERSON>", "resetToDefault": "Reset ke default", "description": "Tentukan keahlian dan k<PERSON><PERSON><PERSON>ian Roo untuk mode ini. Deskripsi ini membentuk bagaimana Roo mempresentasikan dirinya dan mendekati tugas."}, "description": {"title": "<PERSON><PERSON><PERSON><PERSON> (untuk manusia)", "resetToDefault": "Setel ulang ke deskripsi default", "description": "Desk<PERSON><PERSON> singkat yang ditampilkan di dropdown pemilih mode."}, "whenToUse": {"title": "<PERSON><PERSON> (opsional)", "description": "Jelaskan kapan mode ini harus digunakan. Ini membantu Orchestrator memilih mode yang tepat untuk suatu tugas.", "resetToDefault": "Reset ke deskripsi default '<PERSON><PERSON>'"}, "customInstructions": {"title": "Instruksi Kustom Khusus Mode (opsional)", "resetToDefault": "Reset ke default", "description": "Tambahkan panduan perilaku khusus untuk mode {{modeName}}.", "loadFromFile": "Instruksi kustom khusus untuk mode {{mode}} juga dapat dimuat dari folder <span>.roo/rules-{{slug}}/</span> di workspace Anda (.roomodes-{{slug}} dan .clinerules-{{slug}} sudah deprecated dan akan segera berhenti beker<PERSON>)."}, "exportMode": {"title": "Ekspor Mode", "description": "Ekspor mode ini ke file YAML dengan semua aturan yang disertakan untuk berbagi dengan mudah dengan orang lain.", "export": "Ekspor Mode", "exporting": "Mengekspor..."}, "importMode": {"selectLevel": "<PERSON><PERSON><PERSON> di mana akan mengimpor mode ini:", "import": "Impor", "importing": "Mengimpor...", "global": {"label": "Tingkat Global", "description": "Tersedia di semua proyek. Aturan akan digabungkan ke dalam instruksi kustom."}, "project": {"label": "Tingkat Proyek", "description": "Hanya tersedia di ruang kerja ini. Jika mode yang diekspor berisi file aturan, file tersebut akan dibuat ulang di folder .roo/rules-{slug}/."}}, "advanced": {"title": "Lanjutan"}, "globalCustomInstructions": {"title": "Instruksi Kustom untuk Semua Mode", "description": "Instruksi ini berlaku untuk semua mode. <PERSON><PERSON><PERSON> set dasar perilaku yang dapat ditingkatkan oleh instruksi khusus mode di bawah. <0>P<PERSON>jar<PERSON> lebih lanjut</0>", "loadFromFile": "Instruksi juga dapat dimuat dari folder <span>.roo/rules/</span> di workspace Anda (.roorules dan .clinerules sudah deprecated dan akan segera berhenti beker<PERSON>)."}, "systemPrompt": {"preview": "Pratinjau System Prompt", "copy": "Salin system prompt ke clipboard", "title": "System Prompt (mode {{modeName}})"}, "supportPrompts": {"title": "Support Prompts", "resetPrompt": "Reset prompt {{promptType}} ke default", "prompt": "Prompt", "enhance": {"apiConfiguration": "Konfigurasi API", "apiConfigDescription": "<PERSON>a dapat memilih konfigurasi API untuk selalu digunakan untuk meningkatkan prompt, atau gunakan yang sedang dipilih saat ini", "useCurrentConfig": "Gunakan konfigurasi API yang sedang dipilih", "testPromptPlaceholder": "Masukkan prompt untuk menguji peningkatan", "previewButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> Prompt", "testEnhancement": "<PERSON>ji <PERSON>"}, "types": {"ENHANCE": {"label": "Tingkatkan Prompt", "description": "Gunakan peningkatan prompt untuk mendapatkan saran atau perbaikan yang disesuaikan untuk input Anda. Ini memastikan Roo memahami maksud Anda dan memberikan respons terbaik. Tersedia melalui ikon ✨ di chat."}, "EXPLAIN": {"label": "<PERSON><PERSON><PERSON>", "description": "Dapatkan penjelasan detail tentang snippet kode, fungsi, atau seluruh file. Berguna untuk memahami kode kompleks atau mempelajari pola baru. Tersedia di code actions (ikon lightbulb di editor) dan menu konteks editor (klik kanan pada kode yang dipilih)."}, "FIX": {"label": "Perbaiki Masalah", "description": "Dapatkan bantuan mengidentifikasi dan menyelesaikan bug, error, atau masalah kualitas kode. Memberikan panduan langkah demi langkah untuk memperbaiki masalah. Tersedia di code actions (ikon lightbulb di editor) dan menu konteks editor (klik kanan pada kode yang dipilih)."}, "IMPROVE": {"label": "Tingkatkan Kode", "description": "Terima saran untuk optimisasi kode, praktik yang lebih baik, dan perbaikan arsitektur sambil mempertahankan fungsionalitas. Tersedia di code actions (ikon lightbulb di editor) dan menu konteks editor (klik kanan pada kode yang dipilih)."}, "ADD_TO_CONTEXT": {"label": "Tambahkan ke Konteks", "description": "Tambahkan konteks ke tugas atau percakapan saat ini. Berguna untuk memberikan informasi tambahan atau klarifikasi. Tersedia di code actions (ikon lightbulb di editor) dan menu konteks editor (klik kanan pada kode yang dipilih)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Tambahkan Konten Terminal ke Konteks", "description": "Tambahkan output terminal ke tugas atau percakapan saat ini. Berguna untuk memberikan output perintah atau log. Tersedia di menu konteks terminal (klik kanan pada konten terminal yang dipilih)."}, "TERMINAL_FIX": {"label": "Perbaiki Perintah Terminal", "description": "Dapatkan bantuan memperbaiki perintah terminal yang gagal atau perlu perbaikan. Tersedia di menu konteks terminal (klik kanan pada konten terminal yang dipilih)."}, "TERMINAL_EXPLAIN": {"label": "Jelaskan Perintah Terminal", "description": "Dapatkan penjelasan detail tentang perintah terminal dan outputnya. Tersedia di menu konteks terminal (klik kanan pada konten terminal yang dipilih)."}, "NEW_TASK": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> tugas baru dengan input pengguna. Tersedia di Command Palette."}}}, "advancedSystemPrompt": {"title": "Lanjutan: Override System Prompt", "description": "<2>⚠️ Peringatan:</2> Fitur lanjutan ini melewati pengamanan. <1>BACA INI SEBELUM MENGGUNAKAN!</1>Override system prompt default dengan membuat file di <span>.roo/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Buat Mode Baru", "close": "<PERSON><PERSON><PERSON>", "name": {"label": "<PERSON><PERSON>", "placeholder": "Masukkan nama mode"}, "slug": {"label": "Slug", "description": "Slug digunakan dalam URL dan nama file. Harus huruf kecil dan hanya berisi huruf, ang<PERSON>, dan tanda hubung."}, "saveLocation": {"label": "Lokasi <PERSON>", "description": "<PERSON><PERSON><PERSON> di mana menyimpan mode ini. Mode khusus proyek lebih diutamakan daripada mode global.", "global": {"label": "Global", "description": "Tersedia di semua workspace"}, "project": {"label": "<PERSON><PERSON><PERSON> (.room<PERSON>)", "description": "Hanya tersedia di workspace ini, lebih diutamakan daripada global"}}, "roleDefinition": {"label": "<PERSON><PERSON><PERSON>", "description": "Tentukan keahlian dan kep<PERSON><PERSON>ian Roo untuk mode ini."}, "whenToUse": {"label": "<PERSON><PERSON> (opsional)", "description": "Berikan deskripsi yang jelas tentang kapan mode ini paling efektif dan jenis tugas apa yang unggul."}, "tools": {"label": "Tools yang Tersedia", "description": "Pilih tools mana yang dapat digunakan mode ini."}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON> (untuk manusia)", "description": "Desk<PERSON><PERSON> singkat yang ditampilkan di dropdown pemilih mode."}, "customInstructions": {"label": "Instruksi <PERSON> (opsional)", "description": "Tambahkan panduan perilaku khusus untuk mode ini."}, "buttons": {"cancel": "<PERSON><PERSON>", "create": "Buat Mode"}, "deleteMode": "Hapus mode"}, "allFiles": "semua file", "deleteMode": {"title": "Hapus Mode", "message": "Anda yakin ingin menghapus mode \"{{modeName}}\"?", "rulesFolder": "Mode ini memiliki folder aturan di {{folderPath}} yang juga akan dihapus.", "descriptionNoRules": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus mode kustom ini?", "confirm": "Hapus", "cancel": "<PERSON><PERSON>"}}