{"title": "Roo Marketplace", "tabs": {"installed": "Terinstal", "settings": "<PERSON><PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON><PERSON>"}, "done": "Se<PERSON><PERSON>", "refresh": "Refresh", "filters": {"search": {"placeholder": "Cari item marketplace...", "placeholderMcp": "<PERSON>i MCP...", "placeholderMode": "Cari Mode..."}, "type": {"label": "Filter berdasarkan tipe:", "all": "<PERSON><PERSON><PERSON> tipe", "mode": "Mode", "mcpServer": "Server MCP"}, "sort": {"label": "Urutkan berdasarkan:", "name": "<PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON>"}, "tags": {"label": "Filter berdasarkan tag:", "clear": "Hapus tag", "placeholder": "Ketik untuk mencari dan memilih tag...", "noResults": "Tidak ada tag yang cocok", "selected": "Menampilkan item dengan tag yang dipilih", "clickToFilter": "Klik tag untuk memfilter item"}, "none": "Tidak Ada"}, "type-group": {"modes": "Mode", "mcps": "Server MCP"}, "items": {"empty": {"noItems": "Tidak ada item marketplace ditemukan", "withFilters": "Coba sesuaikan filter Anda", "noSources": "Coba tambahkan sumber di tab Sumber", "adjustFilters": "Coba sesuaikan filter atau kata kunci pencarian <PERSON>a", "clearAllFilters": "Hapus semua filter"}, "count": "{{count}} item ditemukan", "components": "{{count}} kom<PERSON><PERSON>", "matched": "{{count}} cocok", "refresh": {"button": "Refresh", "refreshing": "Merefresh...", "mayTakeMoment": "Ini mungkin membutuhkan waktu sebentar."}, "card": {"by": "oleh {{author}}", "from": "dari {{source}}", "install": "Instal", "installProject": "Instal", "installGlobal": "Instal (Global)", "remove": "Hapus", "removeProject": "Hapus", "removeGlobal": "Ha<PERSON> (Global)", "viewSource": "Lihat", "viewOnSource": "<PERSON>hat di {{source}}", "noWorkspaceTooltip": "Buka workspace untuk menginstal item marketplace", "installed": "Terinstal", "removeProjectTooltip": "<PERSON>pus dari proyek saat ini", "removeGlobalTooltip": "Hapus dari kon<PERSON> global", "actionsMenuLabel": "<PERSON><PERSON><PERSON>"}}, "install": {"title": "Instal {{name}}", "titleMode": "Instal Mode {{name}}", "titleMcp": "Instal MCP {{name}}", "scope": "<PERSON><PERSON><PERSON><PERSON>", "project": "Proyek (workspace saat ini)", "global": "Global (semua workspace)", "method": "<PERSON><PERSON>", "prerequisites": "<PERSON><PERSON><PERSON><PERSON>", "configuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "configurationDescription": "Konfigurasi parameter yang diperlukan untuk server MCP ini", "button": "Instal", "successTitle": "{{name}} Te<PERSON><PERSON>", "successDescription": "Instalasi ber<PERSON> disel<PERSON>", "installed": "<PERSON><PERSON><PERSON><PERSON> diinstal!", "whatNextMcp": "<PERSON><PERSON> se<PERSON> dapat mengkonfigurasi dan menggunakan server MCP ini. Klik ikon MCP di sidebar untuk beralih tab.", "whatNextMode": "<PERSON>a sekarang dapat menggunakan mode ini. Klik ikon Mode di sidebar untuk beralih tab.", "done": "Se<PERSON><PERSON>", "goToMcp": "Ke Tab MCP", "goToModes": "Ke Pengaturan Mode", "moreInfoMcp": "Lihat dokumentasi MCP {{name}}", "validationRequired": "<PERSON><PERSON><PERSON> berikan nilai untuk {{paramName}}"}, "sources": {"title": "Konfigurasi Sumber Marketplace", "description": "Tambahkan repositori Git yang berisi item marketplace. Repositori ini akan diambil saat menjelajahi marketplace.", "add": {"title": "Tambah Sumber Baru", "urlPlaceholder": "URL repositori Git (misalnya, https://github.com/username/repo)", "urlFormats": "Format yang didukung: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git), atau protokol Git (git://github.com/username/repo.git)", "namePlaceholder": "<PERSON><PERSON> ta<PERSON> (maks 20 karakter)", "button": "Tambah Sumber"}, "current": {"title": "Sumber <PERSON><PERSON>", "empty": "Tidak ada sumber yang dikonfigurasi. Tambahkan sumber untuk memulai.", "refresh": "Refresh sumber ini", "remove": "Hapus sumber"}, "errors": {"emptyUrl": "URL tidak boleh kosong", "invalidUrl": "Format URL tidak valid", "nonVisibleChars": "URL mengandung karakter tidak terlihat selain spasi", "invalidGitUrl": "URL harus berupa URL repositori Git yang valid (misalnya, https://github.com/username/repo)", "duplicateUrl": "URL ini sudah ada dalam daftar (pencocokan tidak peka huruf besar/kecil dan spasi)", "nameTooLong": "Nama harus 20 karakter atau kurang", "nonVisibleCharsName": "Nama mengandung karakter tidak terlihat selain spasi", "duplicateName": "<PERSON>a ini sudah digunakan (pencocokan tidak peka huruf besar/kecil dan spasi)", "emojiName": "<PERSON><PERSON><PERSON> emoji dapat menyebabkan masalah tampilan", "maxSources": "Maks<PERSON>l {{max}} sumber diizinkan"}}, "footer": {"issueText": "Menemukan masalah dengan item marketplace atau punya saran untuk yang baru? <0>Buka GitHub issue</0> untuk memberi tahu kami!"}}