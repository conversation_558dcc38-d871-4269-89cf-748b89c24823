{"greeting": "Welcome to NeonTractor!", "introduction": "With a range of built-in and extensible Modes, NeonTractor lets you plan, architect, code, debug and boost your productivity like never before.", "notice": "To get started, this extension needs an API provider.", "start": "Let's go!", "routers": {"requesty": {"description": "Your optimized LLM router", "incentive": "$1 free credit"}, "openrouter": {"description": "A unified interface for LLMs"}}, "chooseProvider": "To do its magic, NeonTractor needs an API key.", "startRouter": "We recommend using an LLM Router:", "startCustom": "Or you can bring your provider API key:", "telemetry": {"title": "Help Improve NeonTractor", "anonymousTelemetry": "Send anonymous error and usage data to help us fix bugs and improve the extension. No code, prompts, or personal information is ever sent (unless you connect to NeonTractor Cloud). See our <privacyLink>privacy policy</privacyLink> for more details.", "changeSettings": "You can always change this at the bottom of the <settingsLink>settings</settingsLink>", "settings": "settings", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "importSettings": "Import Settings"}