{"unknownError": "Onbekende fout", "authenticationFailed": "Insluitingen maken mislukt: Authenticatie mislukt. Controleer je API-sleutel.", "failedWithStatus": "Insluitingen maken mislukt na {{attempts}} pogingen: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Insluitingen maken mislukt na {{attempts}} pogingen: {{errorMessage}}", "failedMaxAttempts": "Insluitingen maken mislukt na {{attempts}} pogingen", "textExceedsTokenLimit": "Tekst op index {{index}} overschrijdt de maximale tokenlimiet ({{itemTokens}} > {{maxTokens}}). Wordt overgeslagen.", "rateLimitRetry": "<PERSON>nelheids<PERSON><PERSON> bere<PERSON>t, opnieuw proberen over {{delayMs}}ms (poging {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "<PERSON>n foutinhoud niet lezen", "requestFailed": "Ollama API-verzoek mislukt met status {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Ongeldige responsstructuur van Ollama API: \"embeddings\" array niet gevonden of is geen array.", "embeddingFailed": "Ollama insluiting mislukt: {{message}}", "serviceNotRunning": "Ollama-service draait niet op {{baseUrl}}", "serviceUnavailable": "Ollama-service is niet be<PERSON> (status: {{status}})", "modelNotFound": "Ollama-model niet gevo<PERSON>: {{modelId}}", "modelNotEmbeddingCapable": "Ollama-model is niet in staat tot insluiten: {{modelId}}", "hostNotFound": "Ollama-host niet gevo<PERSON>: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Onbekende fout bij verwerken van bestand {{filePath}}", "unknownErrorDeletingPoints": "Onbekende fout bij verwijderen van punten voor {{filePath}}", "failedToProcessBatchWithError": "Verwerken van batch mislukt na {{maxRetries}} pogingen: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Kan geen verbinding maken met Qdrant vectordatabase. Zorg ervoor dat Qdrant draait en toegankelijk is op {{qdrantUrl}}. Fout: {{errorMessage}}", "vectorDimensionMismatch": "Kan de vectorindex voor het nieuwe model niet bijwerken. Probeer de index te wissen en opnieuw te beginnen. Details: {{errorMessage}}"}, "validation": {"authenticationFailed": "Authenticatie mislukt. Controleer je API-sleutel in de instellingen.", "connectionFailed": "Ver<PERSON><PERSON> met de embedder-service mislukt. Controleer je verbindingsinstellingen en zorg ervoor dat de service draait.", "modelNotAvailable": "Het opgegeven model is niet <PERSON><PERSON><PERSON>. Controleer je modelconfiguratie.", "configurationError": "Ongeldige embedder-configuratie. Controleer je instellingen.", "serviceUnavailable": "De embedder-service is niet be<PERSON>. Zorg ervoor dat deze draait en toegankelijk is.", "invalidEndpoint": "Ongeldig API-eindpunt. Controleer je URL-configuratie.", "invalidEmbedderConfig": "Ongeldige embedder-configuratie. Controleer je instellingen.", "invalidApiKey": "Ongeldige API-sleutel. Controleer je API-sleutelconfiguratie.", "invalidBaseUrl": "Ongeldige basis-URL. Controleer je URL-configuratie.", "invalidModel": "Ongeldig model. Controleer je modelconfiguratie.", "invalidResponse": "Ongeldige reactie van embedder-service. Controleer je configuratie.", "apiKeyRequired": "API-sleutel is vereist voor deze embedder", "baseUrlRequired": "Basis-URL is vereist voor deze embedder"}, "serviceFactory": {"openAiConfigMissing": "OpenAI-configuratie ontbreekt voor het maken van embedder", "ollamaConfigMissing": "Ollama-configuratie ontbreekt voor het maken van embedder", "openAiCompatibleConfigMissing": "OpenAI-compatibele configuratie ontbreekt voor het maken van embedder", "geminiConfigMissing": "Gemini-configuratie ontbre<PERSON>t voor het maken van embedder", "invalidEmbedderType": "Ongeldig embedder-type geconfigureerd: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Kan de vectordimensie voor model '{{modelId}}' met provider '{{provider}}' niet bepalen. Zorg ervoor dat de 'Embedding Dimensie' correct is ingesteld in de OpenAI-compatibele provider-instellingen.", "vectorDimensionNotDetermined": "Kan de vectordimensie voor model '{{modelId}}' met provider '{{provider}}' niet bepalen. Controleer modelprofielen of configuratie.", "qdrantUrlMissing": "Qdrant URL ontbreekt voor het maken van vectoropslag", "codeIndexingNotConfigured": "Kan geen services maken: Code-indexering is niet correct geconfigureerd"}}