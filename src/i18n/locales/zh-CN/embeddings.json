{"unknownError": "未知错误", "authenticationFailed": "创建嵌入失败：身份验证失败。请检查您的 API 密钥。", "failedWithStatus": "尝试 {{attempts}} 次后创建嵌入失败：HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "尝试 {{attempts}} 次后创建嵌入失败：{{errorMessage}}", "failedMaxAttempts": "尝试 {{attempts}} 次后创建嵌入失败", "textExceedsTokenLimit": "索引 {{index}} 处的文本超过最大令牌限制 ({{itemTokens}} > {{maxTokens}})。正在跳过。", "rateLimitRetry": "已达到速率限制，将在 {{delayMs}} 毫秒后重试（尝试次数 {{attempt}}/{{maxRetries}}）", "ollama": {"couldNotReadErrorBody": "无法读取错误内容", "requestFailed": "Ollama API 请求失败，状态码 {{status}} {{statusText}}：{{errorBody}}", "invalidResponseStructure": "Ollama API 响应结构无效：未找到 \"embeddings\" 数组或不是数组。", "embeddingFailed": "Ollama 嵌入失败：{{message}}", "serviceNotRunning": "Ollama 服务未在 {{baseUrl}} 运行", "serviceUnavailable": "Ollama 服务不可用（状态：{{status}}）", "modelNotFound": "未找到 Ollama 模型：{{modelId}}", "modelNotEmbeddingCapable": "Ollama 模型不具备嵌入能力：{{modelId}}", "hostNotFound": "未找到 Ollama 主机：{{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "处理文件 {{filePath}} 时出现未知错误", "unknownErrorDeletingPoints": "删除 {{filePath}} 的数据点时出现未知错误", "failedToProcessBatchWithError": "尝试 {{maxRetries}} 次后批次处理失败：{{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "连接 Qdrant 向量数据库失败。请确保 Qdrant 正在运行并可在 {{qdrantUrl}} 访问。错误：{{errorMessage}}", "vectorDimensionMismatch": "无法更新新模型的向量索引。请尝试清除索引并重新开始。详细信息：{{errorMessage}}"}, "validation": {"authenticationFailed": "身份验证失败。请在设置中检查您的 API 密钥。", "connectionFailed": "连接嵌入器服务失败。请检查您的连接设置并确保服务正在运行。", "modelNotAvailable": "指定的模型不可用。请检查您的模型配置。", "configurationError": "嵌入器配置无效。请查看您的设置。", "serviceUnavailable": "嵌入器服务不可用。请确保它正在运行且可访问。", "invalidEndpoint": "API 端点无效。请检查您的 URL 配置。", "invalidEmbedderConfig": "嵌入器配置无效。请检查您的设置。", "invalidApiKey": "API 密钥无效。请检查您的 API 密钥配置。", "invalidBaseUrl": "基础 URL 无效。请检查您的 URL 配置。", "invalidModel": "模型无效。请检查您的模型配置。", "invalidResponse": "嵌入服务响应无效。请检查您的配置。", "apiKeyRequired": "此嵌入器需要 API 密钥", "baseUrlRequired": "此嵌入器需要基础 URL"}, "serviceFactory": {"openAiConfigMissing": "创建嵌入器缺少 OpenAI 配置", "ollamaConfigMissing": "创建嵌入器缺少 Ollama 配置", "openAiCompatibleConfigMissing": "创建嵌入器缺少 OpenAI 兼容配置", "geminiConfigMissing": "创建嵌入器缺少 Gemini 配置", "invalidEmbedderType": "配置的嵌入器类型无效：{{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "无法确定提供商 '{{provider}}' 的模型 '{{modelId}}' 的向量维度。请确保在 OpenAI 兼容提供商设置中正确设置了「嵌入维度」。", "vectorDimensionNotDetermined": "无法确定提供商 '{{provider}}' 的模型 '{{modelId}}' 的向量维度。请检查模型配置文件或配置。", "qdrantUrlMissing": "创建向量存储缺少 Qdrant URL", "codeIndexingNotConfigured": "无法创建服务：代码索引未正确配置"}}