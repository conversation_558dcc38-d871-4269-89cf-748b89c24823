{"unknownError": "Nieznany błąd", "authenticationFailed": "Nie udało się utworzyć osadzeń: Uwierzytelnianie nie powiodło się. Sprawdź swój klucz API.", "failedWithStatus": "<PERSON><PERSON> udało się utworzyć osadzeń po {{attempts}} próbach: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "<PERSON><PERSON> udało się utworzyć osadzeń po {{attempts}} próbach: {{errorMessage}}", "failedMaxAttempts": "<PERSON>e udało się utworzyć osadzeń po {{attempts}} pr<PERSON><PERSON>", "textExceedsTokenLimit": "Tekst w indeksie {{index}} przekracza maksymalny limit tokenów ({{itemTokens}} > {{maxTokens}}). Pomijanie.", "rateLimitRetry": "Osiągnię<PERSON> limit s<PERSON><PERSON><PERSON><PERSON>, ponawianie za {{delayMs}}ms (próba {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Nie można odczytać treści błędu", "requestFailed": "Żądanie API Ollama nie powiodło się ze statusem {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Nieprawidłowa struktura odpowiedzi z API Ollama: tablica \"embeddings\" nie została znaleziona lub nie jest tablicą.", "embeddingFailed": "Osadzenie Ollama nie powiodło się: {{message}}", "serviceNotRunning": "Usługa Ollama nie jest uruchomiona pod adresem {{baseUrl}}", "serviceUnavailable": "Usługa Ollama jest niedostępna (status: {{status}})", "modelNotFound": "Nie znaleziono modelu Ollama: {{modelId}}", "modelNotEmbeddingCapable": "Model Ollama nie jest zdolny do osadzania: {{modelId}}", "hostNotFound": "Nie znaleziono hosta Ollama: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Nieznany błąd podczas przetwarzania pliku {{filePath}}", "unknownErrorDeletingPoints": "Nieznany błąd podczas usuwania punktów dla {{filePath}}", "failedToProcessBatchWithError": "<PERSON>e udało się przetworzyć partii po {{maxRetries}} próbach: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Nie udało się połączyć z bazą danych wektorowych Qdrant. Upew<PERSON>j się, że Qdrant jest uruchomiony i dostępny pod adresem {{qdrantUrl}}. Błąd: {{errorMessage}}", "vectorDimensionMismatch": "<PERSON>e udało się zaktualizować indeksu wektorowego dla nowego modelu. Spróbuj wyczyścić indeks i zacząć od nowa. Szczegóły: {{errorMessage}}"}, "validation": {"authenticationFailed": "Uwierzytelnianie nie powiodło się. Sprawdź swój klucz API w ustawieniach.", "connectionFailed": "Nie udało się połączyć z usługą embeddera. Sprawdź ustawienia połączenia i upewnij się, że usługa jest uruchomiona.", "modelNotAvailable": "Określony model jest niedostę<PERSON>ny. Sprawdź konfigurację modelu.", "configurationError": "Nieprawidłowa konfiguracja embeddera. Sprawdź swoje ustawienia.", "serviceUnavailable": "Usługa embeddera jest niedostępna. Upewnij się, że jest uruchomiona i dostępna.", "invalidEndpoint": "Nieprawidłowy punkt końcowy API. Sprawdź konfigurację adresu URL.", "invalidEmbedderConfig": "Nieprawidłowa konfiguracja embeddera. Sprawdź swoje ustawienia.", "invalidApiKey": "Nieprawidłowy klucz API. Sprawdź konfigurację klucza API.", "invalidBaseUrl": "Nieprawidłowy podstawowy adres URL. Sprawdź konfigurację adresu URL.", "invalidModel": "Nieprawidłowy model. Sprawdź konfigurację modelu.", "invalidResponse": "Nieprawidłowa odpowiedź z usługi embedder. Sprawdź swoją konfigurację.", "apiKeyRequired": "Klu<PERSON> <PERSON> jest wymagany dla tego embeddera", "baseUrlRequired": "Podstawowy adres URL jest wymagany dla tego embeddera"}, "serviceFactory": {"openAiConfigMissing": "Brak konfiguracji OpenAI do utworzenia embeddera", "ollamaConfigMissing": "Brak konfiguracji Ollama do utworzenia embeddera", "openAiCompatibleConfigMissing": "Brak konfiguracji kompatybilnej z OpenAI do utworzenia embeddera", "geminiConfigMissing": "Brak konfiguracji Gemini do utworzenia embeddera", "invalidEmbedderType": "Skonfigurowano nieprawidłowy typ embeddera: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "<PERSON>e można <PERSON> wymiaru wektora dla modelu '{{modelId}}' z dostawcą '{{provider}}'. <PERSON><PERSON><PERSON><PERSON>, że 'Wymiar osadzania' jest poprawnie ustawiony w ustawieniach dostawcy kompatybilnego z OpenAI.", "vectorDimensionNotDetermined": "<PERSON>e moż<PERSON> wymiaru wektora dla modelu '{{modelId}}' z dostawcą '{{provider}}'. Sprawdź profile modelu lub konfigurację.", "qdrantUrlMissing": "Brak adresu URL Qdrant do utworzenia magazynu wektorów", "codeIndexingNotConfigured": "Nie można utworzyć usług: Indeksowanie kodu nie jest poprawnie skonfigurowane"}}