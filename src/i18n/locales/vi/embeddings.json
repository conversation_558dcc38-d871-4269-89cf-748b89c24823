{"unknownError": "Lỗi không xác định", "authenticationFailed": "<PERSON>hông thể tạo nhúng: <PERSON><PERSON><PERSON> thực không thành công. <PERSON>ui lòng kiểm tra khóa API của bạn.", "failedWithStatus": "<PERSON><PERSON><PERSON><PERSON> thể tạo nhúng sau {{attempts}} lần thử: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "<PERSON><PERSON><PERSON><PERSON> thể tạo nhúng sau {{attempts}} lần thử: {{errorMessage}}", "failedMaxAttempts": "<PERSON><PERSON><PERSON><PERSON> thể tạo nhúng sau {{attempts}} lần thử", "textExceedsTokenLimit": "V<PERSON><PERSON> bản tại chỉ mục {{index}} vư<PERSON>t quá giới hạn mã thông báo tối đa ({{itemTokens}} > {{maxTokens}}). Bỏ qua.", "rateLimitRetry": "<PERSON><PERSON> đạt đến giới hạn tốc độ, thử lại sau {{delayMs}}ms (lần thử {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "<PERSON><PERSON><PERSON>ng thể đọc nội dung lỗi", "requestFailed": "<PERSON><PERSON><PERSON> cầu API Ollama thất bại với trạng thái {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> phản hồi không hợp lệ từ API Ollama: không tìm thấy mảng \"embeddings\" hoặc không phải là mảng.", "embeddingFailed": "<PERSON><PERSON><PERSON><PERSON> thất bại: {{message}}", "serviceNotRunning": "<PERSON><PERSON><PERSON> v<PERSON> không chạy tại {{baseUrl}}", "serviceUnavailable": "<PERSON><PERSON><PERSON> v<PERSON> không khả dụng (trạng thái: {{status}})", "modelNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mô hình <PERSON>: {{modelId}}", "modelNotEmbeddingCapable": "<PERSON><PERSON> hình <PERSON> không có khả năng nhúng: {{modelId}}", "hostNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy máy chủ <PERSON>ma: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Lỗi không x<PERSON>c định khi xử lý tệp {{filePath}}", "unknownErrorDeletingPoints": "Lỗi không xác định khi xóa điểm cho {{filePath}}", "failedToProcessBatchWithError": "<PERSON><PERSON><PERSON><PERSON> thể xử lý lô sau {{maxRetries}} lần thử: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "<PERSON><PERSON><PERSON>ng thể kết nối với cơ sở dữ liệu vector Qdrant. Vui lòng đảm bảo Qdrant đang chạy và có thể truy cập tại {{qdrantUrl}}. Lỗi: {{errorMessage}}", "vectorDimensionMismatch": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật chỉ mục vector cho mô hình mới. <PERSON><PERSON> lòng thử xóa chỉ mục và bắt đầu lại. <PERSON> tiết: {{errorMessage}}"}, "validation": {"authenticationFailed": "<PERSON><PERSON><PERSON> thực không thành công. <PERSON><PERSON> lòng kiểm tra khóa API của bạn trong cài đặt.", "connectionFailed": "<PERSON>h<PERSON>ng thể kết nối với dịch vụ nhúng. Vui lòng kiểm tra cài đặt kết nối của bạn và đảm bảo dịch vụ đang chạy.", "modelNotAvailable": "<PERSON>ô hình được chỉ định không có sẵn. <PERSON><PERSON> lòng kiểm tra cấu hình mô hình của bạn.", "configurationError": "<PERSON><PERSON><PERSON> hình nhúng không hợp lệ. <PERSON><PERSON> lòng xem lại cài đặt của bạn.", "serviceUnavailable": "<PERSON><PERSON><PERSON> vụ nhúng không có sẵn. Vui lòng đảm bảo nó đang chạy và có thể truy cập đư<PERSON>.", "invalidEndpoint": "<PERSON>iểm cuối API không hợp lệ. <PERSON><PERSON> lòng kiểm tra cấu hình URL của bạn.", "invalidEmbedderConfig": "<PERSON><PERSON><PERSON> hình nhúng không hợp lệ. <PERSON><PERSON> lòng kiểm tra cài đặt của bạn.", "invalidApiKey": "Khóa API không hợp lệ. <PERSON>ui lòng kiểm tra cấu hình khóa API của bạn.", "invalidBaseUrl": "URL cơ sở không hợp lệ. <PERSON><PERSON> lòng kiểm tra cấu hình URL của bạn.", "invalidModel": "<PERSON><PERSON> hình không hợp lệ. <PERSON>ui lòng kiểm tra cấu hình mô hình của bạn.", "invalidResponse": "<PERSON><PERSON><PERSON> hồ<PERSON> không hợp lệ từ dịch vụ embedder. <PERSON><PERSON> lòng kiểm tra cấu hình của bạn.", "apiKeyRequired": "Cần có khóa API cho trình nhúng này", "baseUrlRequired": "<PERSON><PERSON>n có URL cơ sở cho trình nhúng này"}, "serviceFactory": {"openAiConfigMissing": "<PERSON><PERSON><PERSON><PERSON> cấu hình OpenAI để tạo embedder", "ollamaConfigMissing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u hình <PERSON> để tạo embedder", "openAiCompatibleConfigMissing": "<PERSON><PERSON><PERSON><PERSON> cấu hình tương thích OpenAI để tạo embedder", "geminiConfigMissing": "<PERSON><PERSON><PERSON><PERSON> cấu hình Gemini để tạo embedder", "invalidEmbedderType": "<PERSON><PERSON><PERSON> embedder <PERSON><PERSON><PERSON><PERSON> cấu hình không hợp lệ: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "<PERSON><PERSON><PERSON><PERSON> thể xác định kích thước vector cho mô hình '{{modelId}}' với nhà cung cấp '{{provider}}'. <PERSON><PERSON><PERSON> đảm bảo '<PERSON><PERSON><PERSON> thước Embedding' đ<PERSON><PERSON><PERSON> cài đặt đúng trong cài đặt nhà cung cấp tương thích OpenAI.", "vectorDimensionNotDetermined": "<PERSON><PERSON><PERSON><PERSON> thể xác định kích thước vector cho mô hình '{{modelId}}' với nhà cung cấp '{{provider}}'. <PERSON><PERSON><PERSON> tra hồ sơ mô hình hoặc cấu hình.", "qdrantUrlMissing": "<PERSON><PERSON><PERSON>u URL Qdrant để tạo kho lưu trữ vector", "codeIndexingNotConfigured": "<PERSON>h<PERSON>ng thể tạo dịch vụ: Lậ<PERSON> chỉ mục mã không được cấu hình đúng cách"}}