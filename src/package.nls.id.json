{"extension.displayName": "Roo Code (prev. Roo Cline)", "extension.description": "<PERSON>mbang AI lengkap di editor kamu.", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.activitybar.title": "Roo Code", "views.sidebar.name": "Roo Code", "command.newTask.title": "<PERSON><PERSON>", "command.mcpServers.title": "Server MCP", "command.prompts.title": "Mode", "command.history.title": "Riwayat", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "<PERSON><PERSON> di Editor", "command.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "command.documentation.title": "Dokumentasi", "command.openInNewTab.title": "<PERSON><PERSON> <PERSON>", "command.explainCode.title": "<PERSON><PERSON><PERSON>", "command.fixCode.title": "Perbaiki Kode", "command.improveCode.title": "Tingkatkan Kode", "command.addToContext.title": "Tambahkan ke Konteks", "command.focusInput.title": "Fokus ke Field Input", "command.setCustomStoragePath.title": "Atur Path Penyimpanan Kustom", "command.importSettings.title": "<PERSON><PERSON><PERSON>", "command.terminal.addToContext.title": "Tambahkan Konten Terminal ke Konteks", "command.terminal.fixCommand.title": "Perbaiki Perintah Ini", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON>", "command.acceptInput.title": "Terima Input/Saran", "configuration.title": "Roo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON> yang dapat dijalankan secara otomatis ketika 'Selalu setujui operasi eksekusi' diak<PERSON><PERSON>kan", "commands.deniedCommands.description": "<PERSON>walan perintah yang akan otomatis ditolak tanpa meminta persetujuan. Jika terjadi konflik dengan perintah yang diizink<PERSON>, pencocokan awalan terpanjang akan diprioritaskan. Tambahkan * untuk menolak semua perintah.", "commands.commandExecutionTimeout.description": "<PERSON><PERSON><PERSON> maksimum dalam detik untuk menunggu eksekusi perintah selesai sebelum timeout (0 = tanpa timeout, 1-600s, default: 0s)", "settings.vsCodeLmModelSelector.description": "Pengaturan untuk API Model Bahasa VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Vendor dari model bahasa (misalnya copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON><PERSON><PERSON> dari model bahasa (misalnya gpt-4)", "settings.customStoragePath.description": "Path penyimpanan kustom. Biarkan kosong untuk menggunakan lokasi default. Mendukung path absolut (misalnya 'D:\\RooCodeStorage')", "settings.enableCodeActions.description": "Aktifkan perbaikan cepat Roo Code.", "settings.autoImportSettingsPath.description": "Path ke file konfigurasi RooCode untuk diimpor secara otomatis saat ekstensi dimulai. Mendukung path absolut dan path relatif terhadap direktori home (misalnya '~/Documents/roo-code-settings.json'). Biarkan kosong untuk menonaktifkan impor otomatis."}